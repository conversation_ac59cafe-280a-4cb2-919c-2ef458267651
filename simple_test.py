print("=== Python环境测试 ===")
print("测试开始...")

import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

try:
    import mph
    print("✓ MPh库导入成功")
except Exception as e:
    print(f"✗ MPh库导入失败: {e}")

try:
    import jpype
    print("✓ JPype库导入成功")
except Exception as e:
    print(f"✗ JPype库导入失败: {e}")

try:
    import numpy
    print("✓ NumPy库导入成功")
except Exception as e:
    print(f"✗ NumPy库导入失败: {e}")

import os
print(f"当前工作目录: {os.getcwd()}")
print(f"sim1.mph文件存在: {os.path.exists('sim1.mph')}")

print("测试完成！")
