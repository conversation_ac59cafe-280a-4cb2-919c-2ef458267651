#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试MPh库是否正常工作
"""

import sys
import os

print("Python版本:", sys.version)
print("当前工作目录:", os.getcwd())
print("Python路径:", sys.executable)

try:
    import mph
    print("MPh库导入成功")
    print("MPh版本:", mph.__version__ if hasattr(mph, '__version__') else "版本信息不可用")
except ImportError as e:
    print(f"MPh库导入失败: {e}")
    sys.exit(1)

try:
    import jpype
    print("JPype库导入成功")
    print("JPype版本:", jpype.__version__ if hasattr(jpype, '__version__') else "版本信息不可用")
except ImportError as e:
    print(f"JPype库导入失败: {e}")

# 检查COMSOL安装
comsol_path = "E:\\XiGPrograms\\comsol\\base"
print(f"COMSOL安装路径: {comsol_path}")
print(f"COMSOL路径是否存在: {os.path.exists(comsol_path)}")

# 检查模型文件
model_path = "sim1.mph"
print(f"模型文件: {model_path}")
print(f"模型文件是否存在: {os.path.exists(model_path)}")
if os.path.exists(model_path):
    file_size = os.path.getsize(model_path)
    print(f"模型文件大小: {file_size} 字节 ({file_size/1024/1024:.2f} MB)")

print("\n尝试启动COMSOL客户端...")
try:
    client = mph.start()
    print("COMSOL客户端启动成功！")
    print(f"客户端信息: {client}")
    
    # 尝试获取版本信息
    try:
        version = client.version
        print(f"COMSOL版本: {version}")
    except Exception as e:
        print(f"获取版本信息失败: {e}")
    
    # 断开连接
    client.disconnect()
    print("客户端连接已断开")
    
except Exception as e:
    print(f"启动COMSOL客户端失败: {e}")
    print("错误类型:", type(e).__name__)
    import traceback
    traceback.print_exc()
