#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
COMSOL仿真文件分析脚本
使用MPh库连接COMSOL Server并分析sim1.mph文件
"""

import mph
import numpy as np
import os
import sys

def connect_to_comsol():
    """连接到COMSOL Server"""
    try:
        print("正在启动COMSOL Server...")
        # 启动COMSOL客户端
        client = mph.start()
        print(f"COMSOL Server连接成功！")
        print(f"COMSOL版本: {client.version}")
        return client
    except Exception as e:
        print(f"连接COMSOL Server失败: {e}")
        return None

def load_model(client, model_path):
    """加载COMSOL模型文件"""
    try:
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            return None
        
        print(f"正在加载模型文件: {model_path}")
        model = client.load(model_path)
        print("模型加载成功！")
        return model
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None

def analyze_model(model):
    """分析COMSOL模型的详细信息"""
    try:
        print("\n" + "="*50)
        print("模型分析报告")
        print("="*50)
        
        # 基本信息
        print(f"模型名称: {model.name}")
        print(f"模型描述: {model.description}")
        
        # 几何信息
        print(f"\n几何维度: {model.geom}")
        if hasattr(model, 'geom') and model.geom:
            for geom_name in model.geom:
                geom = model.geom[geom_name]
                print(f"  几何对象 '{geom_name}': {geom}")
        
        # 物理场信息
        print(f"\n物理场: {model.physics}")
        if hasattr(model, 'physics') and model.physics:
            for physics_name in model.physics:
                physics = model.physics[physics_name]
                print(f"  物理场 '{physics_name}': {physics}")
                print(f"    类型: {physics.type if hasattr(physics, 'type') else '未知'}")
        
        # 材料信息
        print(f"\n材料: {model.materials}")
        if hasattr(model, 'materials') and model.materials:
            for material_name in model.materials:
                material = model.materials[material_name]
                print(f"  材料 '{material_name}': {material}")
        
        # 网格信息
        print(f"\n网格: {model.mesh}")
        if hasattr(model, 'mesh') and model.mesh:
            for mesh_name in model.mesh:
                mesh = model.mesh[mesh_name]
                print(f"  网格 '{mesh_name}': {mesh}")
        
        # 求解器信息
        print(f"\n求解器: {model.studies}")
        if hasattr(model, 'studies') and model.studies:
            for study_name in model.studies:
                study = model.studies[study_name]
                print(f"  研究 '{study_name}': {study}")
        
        # 参数信息
        print(f"\n参数:")
        try:
            parameters = model.parameters
            if parameters:
                for param_name, param_value in parameters.items():
                    print(f"  {param_name}: {param_value}")
            else:
                print("  无参数定义")
        except:
            print("  无法获取参数信息")
        
        # 尝试获取更多详细信息
        print(f"\n模型属性:")
        try:
            # 获取所有可用的属性
            attrs = [attr for attr in dir(model) if not attr.startswith('_')]
            for attr in attrs[:10]:  # 只显示前10个属性
                try:
                    value = getattr(model, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                except:
                    pass
        except Exception as e:
            print(f"  获取属性失败: {e}")
            
    except Exception as e:
        print(f"分析模型时出错: {e}")

def main():
    """主函数"""
    print("COMSOL仿真文件分析工具")
    print("="*30)
    
    # 连接COMSOL
    client = connect_to_comsol()
    if not client:
        print("无法连接到COMSOL Server，程序退出")
        return
    
    # 加载模型
    model_path = "sim1.mph"
    model = load_model(client, model_path)
    if not model:
        print("无法加载模型文件，程序退出")
        client.disconnect()
        return
    
    # 分析模型
    analyze_model(model)
    
    # 清理资源
    print(f"\n分析完成，正在断开连接...")
    client.disconnect()
    print("连接已断开")

if __name__ == "__main__":
    main()
